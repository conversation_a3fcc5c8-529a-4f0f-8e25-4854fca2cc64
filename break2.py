from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.edge.service import Service as EdgeService
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from bs4 import BeautifulSoup
import os
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import messagebox
import threading
import time

# 定義 URL
LOGIN_URL = "http://gas.gmt.com.tw/door/login.php"

# 查表：username 對應 qid
user_qid_mapping = {
    "allen.kao": "H0552",
    "destin": "93147",
    "sp.hsu": "I0597"
}

# 獲取當前日期並動態生成 ATTENDANCE_URL
current_year = datetime.now().year
current_month = datetime.now().month
if current_month < 10:
    current_month = f"0{current_month}"
current_day = datetime.now().day
if current_day < 10:
    current_day = f"0{current_day}"
current_date = f"{current_year}-{current_month}-{current_day}"
username = os.environ['USERNAME']
qid = user_qid_mapping.get(username, "ABCDE")
ATTENDANCE_URL = f"http://gas.gmt.com.tw/door/detail.php?qy={current_year}&qm={current_month}&qid={qid}"

# 獲取當前用戶名稱和網域
domain = os.environ['USERDOMAIN']
full_username = f"{domain}\\{username}"
user_username = username
user_profile_path = os.path.join(os.path.expanduser("~"), "AppData", "Local", "Microsoft", "Edge", "User Data", "Default")

# 定義提醒視窗類
class ReminderWindow:
    def __init__(self, title, message):
        self.root = tk.Tk()
        self.root.title(title)
        self.root.geometry("250x150")  # 縮小視窗大小
        self.root.configure(bg="#f0f0f0")
        self.root.attributes("-topmost", True)  # 確保視窗在最上層

        label = tk.Label(self.root, text=message, bg="#f0f0f0", font=("Segoe UI", 10), wraplength=200)
        label.pack(pady=15)

        button = tk.Button(self.root, text="確認", command=self.root.destroy, bg="#4CAF50", fg="white", font=("Segoe UI", 9))
        button.pack(pady=10)

    def show(self):
        self.root.mainloop()

# 密碼輸入視窗
def get_password():
    password = None
    def submit_password():
        nonlocal password
        password = entry.get()
        root.destroy()

    root = tk.Tk()
    root.title("輸入密碼")
    root.geometry("200x130")
    root.configure(bg="#f0f0f0")
    root.attributes("-topmost", True)

    label = tk.Label(root, text="請輸入密碼:", bg="#f0f0f0")
    label.pack(pady=10)
    entry = tk.Entry(root, show="*")
    entry.pack(pady=5)
    entry.bind("<Return>", lambda event: submit_password())
    entry.focus_set()
    button = tk.Button(root, text="提交", command=submit_password)
    button.pack(pady=10)
    root.mainloop()
    return password

# 顯示提醒視窗
def remind_to_drink():
    """顯示第一個提醒視窗"""
    try:
        current_time = datetime.now().strftime("%H:%M:%S")  # 獲取當前時間
        message = f"現在時間: {current_time}\n保持水分有助於提高專注力\n促進新陳代謝！"
        window = ReminderWindow("喝水300CC", message)
        window.show()
        print("第一個提醒已確認")
    except Exception as e:
        print(f"第一個彈窗失敗: {e}")

def remind_to_drink2():
    """顯示第二個提醒視窗"""
    try:
        current_time = datetime.now().strftime("%H:%M:%S")  # 獲取當前時間
        message = f"現在時間: {current_time}\n適量喝水能減少疲勞\n保持身體健康！"
        window = ReminderWindow("喝水100CC", message)
        window.show()
        print("第二個提醒已確認")
    except Exception as e:
        print(f"第二個彈窗失敗: {e}")

# 執行單個提醒的函數
def run_reminder(reminder_time, reminder_func):
    """在指定的提醒時間執行提醒函數"""
    current_time = datetime.now()
    if current_time < reminder_time:
        wait_seconds = (reminder_time - current_time).total_seconds()
        print(f"等待 {wait_seconds:.2f} 秒執行提醒...")
        time.sleep(wait_seconds)
        reminder_func()
    else:
        print(f"提醒時間 {reminder_time.strftime('%H:%M:%S')} 已過，跳過提醒")

# 排程提醒函數
def schedule_reminders(check_out_time):
    """Schedule reminders 30 minutes and 10 minutes before check-out time."""
    if not check_out_time:
        print("No check-out time provided, cannot schedule reminders")
        return

    # 計算提醒時間
    reminder_time_30min = check_out_time - timedelta(minutes=30)
    reminder_time_10min = check_out_time - timedelta(minutes=10)
    current_time = datetime.now()

    print(f"Current time: {current_time.strftime('%H:%M:%S')}")
    print(f"Check-out time: {check_out_time.strftime('%H:%M:%S')}")
    print(f"First reminder (300CC) scheduled for: {reminder_time_30min.strftime('%H:%M:%S')}")
    print(f"Second reminder (100CC) scheduled for: {reminder_time_10min.strftime('%H:%M:%S')}")

    # 為每個提醒創建獨立的執行緒
    thread1 = threading.Thread(target=run_reminder, args=(reminder_time_30min, remind_to_drink))
    thread2 = threading.Thread(target=run_reminder, args=(reminder_time_10min, remind_to_drink2))

    # 啟動執行緒
    thread1.start()
    thread2.start()

    # 等待執行緒完成
    thread1.join()
    thread2.join()

# 登入函數
def login(password):
    options = webdriver.EdgeOptions()
    options.add_argument(f"user-data-dir={user_profile_path}")
    options.add_argument("--headless")
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1920,1080")

    try:
        # 使用 webdriver_manager 自動下載並安裝匹配版本的 EdgeDriver
        service = EdgeService(EdgeChromiumDriverManager().install())
        driver = webdriver.Edge(service=service, options=options)
        print("WebDriver 啟動，使用無頭模式")
    except Exception as e:
        print(f"WebDriver 啟動失敗: {e}")
        print("請檢查：")
        print("1. Microsoft Edge 瀏覽器是否已安裝")
        print("2. webdriver_manager 是否已安裝 (pip install webdriver-manager)")
        print("3. 確保有網路連線以下載匹配的 EdgeDriver")
        return None

    print(f"訪問登入頁面: {LOGIN_URL}")
    try:
        driver.get(LOGIN_URL)
        WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.NAME, "User")))
        print("登入頁面載入完成")
        driver.find_element(By.NAME, "User").send_keys(user_username)
        print(f"輸入帳號: {user_username}")
        driver.find_element(By.NAME, "Pass").send_keys(password)
        print("輸入密碼")
        driver.find_element(By.XPATH, "//input[@type='submit']").click()
        print("提交登入表單")
        WebDriverWait(driver, 10).until(EC.url_changes(LOGIN_URL))
        print("登入成功")
        return driver
    except Exception as e:
        print(f"登入失敗: {e}")
        driver.quit()
        return None

# 抓取並解析上下班時間
def scrape_attendance(driver):
    print(f"訪問目標頁面: {ATTENDANCE_URL}")
    check_out_time = None
    try:
        driver.get(ATTENDANCE_URL)
        WebDriverWait(driver, 15).until(EC.presence_of_element_located((By.TAG_NAME, "table")))
        print("頁面載入完成")
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        attendance_table = soup.find('table')
        if not attendance_table:
            print("找不到表格元素，檢查 HTML 結構")
            print(f"完整 HTML 結構（前 1000 字元）: {soup.prettify()[:1000]}")
            return None
        today_data = None
        for row in attendance_table.find_all('tr')[1:]:
            columns = row.find_all('td')
            if len(columns) >= 4:
                date_with_weekday = columns[1].text.strip()
                date_only = date_with_weekday.split(' ')[0]
                if date_only == current_date:
                    no = columns[0].text.strip()
                    check_in = columns[2].text.strip() if columns[2].text.strip() else "N/A"
                    check_out = columns[3].text.strip() if columns[3].text.strip() else "N/A"
                    today_data = (no, date_only, check_in, check_out)
                    print(f"找到今天資料 - 編號: {no}, 日期: {date_only}, 上班時間: {check_in}, 下班時間: {check_out}")
                    break
        if today_data:
            no, date, check_in, check_out = today_data
            if check_in != "N/A":
                check_in_time = datetime.strptime(check_in, "%H:%M:%S").time()
                check_in_datetime = datetime.combine(datetime.now().date(), check_in_time)
                check_out_datetime = check_in_datetime + timedelta(hours=9)
                check_out_calculated = check_out_datetime.strftime("%H:%M:%S")
                print(f"上班時間: {check_in}, 計算下班時間: {check_out_calculated}")
                print(f"上班時間: {check_in}")
                print(f"下班時間: {check_out_calculated}")
                check_out_time = datetime.combine(datetime.now().date(), datetime.strptime(check_out_calculated, "%H:%M:%S").time())
            else:
                print("今日無上班時間記錄")
        else:
            print("未找到今天的日期行")
    except Exception as e:
        print(f"抓取或解析失敗: {e}")
    finally:
        driver.quit()
        print("WebDriver 已釋放")
    return check_out_time

# 主程式
def wait_for_target_time_and_run():
    now = datetime.now()
    target_time = now.replace(hour=13, minute=0, second=0, microsecond=0)
    
    if now < target_time:
        wait_seconds = (target_time - now).total_seconds()
        print(f"等待到 {target_time.strftime('%H:%M:%S')} 執行程式...")
        time.sleep(wait_seconds)
    else:
        print("已過下午1點，立即執行程式...")
    
    print("時間到了，請輸入密碼")
    password = get_password()
    
    if password:
        driver = login(password)
        if driver:
            check_out_time = scrape_attendance(driver)
            if check_out_time:
                schedule_reminders(check_out_time)
            else:
                print("無法獲取下班時間，無法排程提醒")
        else:
            print("登入失敗，程式中止")
    else:
        print("未輸入密碼，程式中止")
    
    print("程式結束")

if __name__ == "__main__":
    print("程式啟動")
    print("程式將在下午1點彈出密碼輸入視窗...")
    
    thread = threading.Thread(target=wait_for_target_time_and_run)
    thread.start()
    thread.join()
    
    print("程式結束")